"""
Modern MMO-style Character Sheet for One Life Isekai
WoW-inspired character sheet with equipment slots and detailed stats
"""

import pygame
import os
from typing import Dict, Any, Optional, Tuple
from utils.constants import COLORS, SCREEN_WIDTH, SCREEN_HEIGHT


class ModernCharacterSheet:
    def __init__(self, screen: pygame.Surface, fonts: Dict[str, pygame.font.Font]):
        self.screen = screen
        self.fonts = fonts
        
        # Improved colors for better readability and contrast
        self.bg_color = (18, 20, 26)  # Darker background
        self.panel_color = (32, 36, 44)  # Higher contrast main panel
        self.section_color = (40, 45, 55)  # Better section contrast
        self.border_color = (90, 100, 115)  # More visible borders
        self.accent_color = (110, 120, 135)  # Clearer accent
        self.text_color = (245, 250, 255)  # High contrast text
        self.secondary_text = (190, 200, 210)  # Secondary info
        self.label_color = (160, 170, 180)  # For labels
        self.gold_color = (255, 215, 0)  # Classic gold
        self.green_color = (85, 220, 85)  # Brighter positive
        self.red_color = (220, 85, 85)  # Brighter negative
        self.blue_color = (85, 140, 220)  # Cleaner blue
        self.orange_color = (255, 165, 0)  # For important stats
        self.unreadable_color = (90, 100, 120)  # More visible mystical symbols
        
        # Layout constants
        self.sheet_rect = pygame.Rect(50, 50, SCREEN_WIDTH - 100, SCREEN_HEIGHT - 100)
        self.equipment_width = 200
        self.stats_width = 300
        
        # Equipment slots positions (left side)
        self.equipment_slots = self._create_equipment_slots()
        
        # Character data
        self.character_data = None
        self.unreadable_lines = []
        self.readable_lines = []
        
        # Load placeholder images
        self._load_placeholder_images()
    
    def _create_equipment_slots(self) -> Dict[str, pygame.Rect]:
        """Create equipment slot rectangles"""
        slots = {}
        slot_size = 40
        spacing = 45
        
        # Left side equipment layout
        eq_x = self.sheet_rect.x + 20
        eq_y = self.sheet_rect.y + 150
        
        # Equipment slots layout (similar to WoW)
        slots['helmet'] = pygame.Rect(eq_x + spacing, eq_y, slot_size, slot_size)
        slots['shoulder'] = pygame.Rect(eq_x, eq_y + spacing, slot_size, slot_size)
        slots['chest'] = pygame.Rect(eq_x + spacing, eq_y + spacing, slot_size, slot_size)
        slots['legs'] = pygame.Rect(eq_x + spacing, eq_y + spacing * 2, slot_size, slot_size)
        slots['belt'] = pygame.Rect(eq_x + spacing, eq_y + spacing * 3, slot_size, slot_size)
        slots['boots'] = pygame.Rect(eq_x + spacing, eq_y + spacing * 4, slot_size, slot_size)
        slots['gloves'] = pygame.Rect(eq_x + spacing * 2, eq_y + spacing, slot_size, slot_size)
        slots['main_hand'] = pygame.Rect(eq_x, eq_y + spacing * 2, slot_size, slot_size)
        slots['off_hand'] = pygame.Rect(eq_x + spacing * 2, eq_y + spacing * 2, slot_size, slot_size)
        slots['ring1'] = pygame.Rect(eq_x, eq_y + spacing * 3, slot_size, slot_size)
        slots['ring2'] = pygame.Rect(eq_x + spacing * 2, eq_y + spacing * 3, slot_size, slot_size)
        slots['necklace'] = pygame.Rect(eq_x + spacing, eq_y - spacing, slot_size, slot_size)
        
        return slots
    
    def _load_placeholder_images(self):
        """Load placeholder images for missing assets"""
        self.placeholder_images = {}
        
        # Create simple colored rectangles as placeholders
        slot_size = 40
        
        # Purple cube for traits/skills
        purple_surface = pygame.Surface((slot_size, slot_size))
        purple_surface.fill((128, 0, 128))
        pygame.draw.rect(purple_surface, (160, 32, 160), purple_surface.get_rect(), 2)
        self.placeholder_images['trait'] = purple_surface
        self.placeholder_images['skill'] = purple_surface
        
        # Gray slot for equipment
        gray_surface = pygame.Surface((slot_size, slot_size))
        gray_surface.fill((60, 60, 60))
        pygame.draw.rect(gray_surface, (100, 100, 100), gray_surface.get_rect(), 2)
        self.placeholder_images['equipment'] = gray_surface
        
        # Character portrait placeholder
        portrait_surface = pygame.Surface((100, 100))
        portrait_surface.fill((80, 80, 80))
        pygame.draw.rect(portrait_surface, (120, 120, 120), portrait_surface.get_rect(), 3)
        self.placeholder_images['portrait'] = portrait_surface
    
    def load_character_image(self, race: str, gender: str) -> pygame.Surface:
        """Load character portrait image"""
        # Try to load specific race/gender image
        image_path = f"pictures/gender/{race}/{gender}.png"
        if os.path.exists(image_path):
            try:
                image = pygame.image.load(image_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Try race-only image
        race_path = f"pictures/race/{race}.png"
        if os.path.exists(race_path):
            try:
                image = pygame.image.load(race_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Use unknown placeholder
        unknown_path = "pictures/race/unknown.png"
        if os.path.exists(unknown_path):
            try:
                image = pygame.image.load(unknown_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Return placeholder
        return self.placeholder_images['portrait']
    
    def initialize_character_sheet(self, character_data: Dict[str, Any]):
        """Initialize the character sheet with unreadable text"""
        self.character_data = character_data
        
        # Create unreadable lines (fantasy script style)
        self.unreadable_lines = []
        self.readable_lines = []
        
        # Generate mystical unreadable symbols for each stat line
        mystical_symbols = "◊◈◇◆◉◎●○◐◑◒◓◔◕◖◗◘◙◚◛◜◝◞◟◠◡◢◣◤◥◦◧◨◩◪◫◬◭◮◯"
        magic_symbols = "⟐⟑⟒⟓⟔⟕⟖⟗⟘⟙⟚⟛⟜⟝⟞⟟⟠⟡⟢⟣⟤⟥⟦⟧⟨⟩⟪⟫⟬⟭⟮⟯"
        rune_symbols = "ᚠᚢᚦᚨᚱᚲᚷᚹᚺᚾᛁᛃᛇᛈᛉᛊᛏᛒᛖᛗᛚᛜᛞᛟ"

        all_symbols = mystical_symbols + magic_symbols + rune_symbols

        for i in range(30):  # Number of stat lines
            # Create varied mystical text with different symbols
            line_length = 12 + (i % 8)
            unreadable_text = ''.join([all_symbols[(i * 3 + j) % len(all_symbols)] for j in range(line_length)])
            self.unreadable_lines.append(unreadable_text)
            self.readable_lines.append("")  # Will be filled during generation
    
    def reveal_stat_line(self, line_index: int, readable_text: str):
        """Reveal a specific stat line with animation"""
        if 0 <= line_index < len(self.readable_lines):
            self.readable_lines[line_index] = readable_text
    
    def draw_character_sheet(self, reveal_progress: float = 0.0):
        """Draw the complete character sheet with modern clean design"""
        # Clear background
        self.screen.fill(self.bg_color)

        # Draw main container with subtle styling
        main_rect = pygame.Rect(20, 20, SCREEN_WIDTH - 40, SCREEN_HEIGHT - 40)
        pygame.draw.rect(self.screen, self.panel_color, main_rect)
        pygame.draw.rect(self.screen, self.border_color, main_rect, 1)

        # Draw sections with better spacing
        self._draw_header_section()
        self._draw_left_panel()  # Equipment + Portrait
        self._draw_center_panel(reveal_progress)  # Main stats
        self._draw_right_panel(reveal_progress)  # Combat stats
        self._draw_biology_panel(reveal_progress)  # Biology stats
        self._draw_bottom_panel(reveal_progress)  # Traits
    
    def _draw_character_header(self):
        """Draw character name, class, level at the top"""
        header_y = self.sheet_rect.y + 20
        
        # Character name (placeholder for now)
        name_text = self.fonts['large'].render("NO NAME", True, self.gold_color)
        name_rect = name_text.get_rect(centerx=self.sheet_rect.centerx, y=header_y)
        self.screen.blit(name_text, name_rect)
        
        # Class and level (placeholders)
        class_text = self.fonts['medium'].render("Adventurer", True, self.text_color)
        level_text = self.fonts['medium'].render("Level 1", True, self.text_color)
        
        class_rect = class_text.get_rect(centerx=self.sheet_rect.centerx - 60, y=header_y + 40)
        level_rect = level_text.get_rect(centerx=self.sheet_rect.centerx + 60, y=header_y + 40)
        
        self.screen.blit(class_text, class_rect)
        self.screen.blit(level_text, level_rect)
    
    def _draw_equipment_slots(self):
        """Draw equipment slots on the left side"""
        for slot_name, slot_rect in self.equipment_slots.items():
            # Draw slot background
            pygame.draw.rect(self.screen, (30, 30, 40), slot_rect)
            pygame.draw.rect(self.screen, self.border_color, slot_rect, 2)
            
            # Draw placeholder equipment
            self.screen.blit(self.placeholder_images['equipment'], slot_rect)
    
    def _draw_character_portrait(self):
        """Draw character portrait"""
        if self.character_data:
            race = self.character_data.get('race', {}).get('name', 'unknown').lower()
            gender = self.character_data.get('gender', {}).get('name', 'unknown').lower()
            
            portrait = self.load_character_image(race, gender)
            portrait_rect = pygame.Rect(self.sheet_rect.x + self.equipment_width + 20, 
                                      self.sheet_rect.y + 80, 100, 100)
            
            self.screen.blit(portrait, portrait_rect)
            pygame.draw.rect(self.screen, self.border_color, portrait_rect, 3)
    
    def _draw_vital_stats(self):
        """Draw HP, MP, XP bars"""
        if not self.character_data:
            return
            
        stats = self.character_data.get('effective_stats', {})
        
        # Calculate values (placeholder calculations)
        max_hp = stats.get('VIT', 20) * 10 + 100
        current_hp = max_hp
        max_mp = stats.get('WILL', 20) * 5 + 50
        current_mp = max_mp
        current_xp = 0
        max_xp = 1000
        
        # Position next to portrait
        bars_x = self.sheet_rect.x + self.equipment_width + 140
        bars_y = self.sheet_rect.y + 100
        bar_width = 200
        bar_height = 20
        
        # HP Bar
        self._draw_stat_bar(bars_x, bars_y, bar_width, bar_height, 
                           current_hp, max_hp, self.red_color, "HP")
        
        # MP Bar
        self._draw_stat_bar(bars_x, bars_y + 30, bar_width, bar_height,
                           current_mp, max_mp, self.blue_color, "MP")
        
        # XP Bar
        self._draw_stat_bar(bars_x, bars_y + 60, bar_width, bar_height,
                           current_xp, max_xp, self.gold_color, "XP")
    
    def _draw_stat_bar(self, x: int, y: int, width: int, height: int, 
                      current: int, maximum: int, color: Tuple[int, int, int], label: str):
        """Draw a stat bar with current/max values"""
        # Background
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(self.screen, (20, 20, 20), bg_rect)
        pygame.draw.rect(self.screen, self.border_color, bg_rect, 2)
        
        # Fill bar
        if maximum > 0:
            fill_width = int((current / maximum) * (width - 4))
            fill_rect = pygame.Rect(x + 2, y + 2, fill_width, height - 4)
            pygame.draw.rect(self.screen, color, fill_rect)
        
        # Text
        text = f"{label}: {current}/{maximum}"
        text_surface = self.fonts['small'].render(text, True, self.text_color)
        text_rect = text_surface.get_rect(center=bg_rect.center)
        self.screen.blit(text_surface, text_rect)
    
    def _draw_mystical_text(self, reveal_progress: float):
        """Draw mystical unreadable text that gradually becomes readable"""
        if not self.character_data:
            return
            
        text_x = self.sheet_rect.x + self.equipment_width + 20
        text_y = self.sheet_rect.y + 200
        line_height = 20
        
        revealed_lines = int(reveal_progress * len(self.unreadable_lines))
        
        for i, (unreadable, readable) in enumerate(zip(self.unreadable_lines, self.readable_lines)):
            y_pos = text_y + i * line_height
            
            if i < revealed_lines and readable:
                # Show readable text
                text_surface = self.fonts['small'].render(readable, True, self.text_color)
                self.screen.blit(text_surface, (text_x, y_pos))
            else:
                # Show unreadable mystical text
                text_surface = self.fonts['small'].render(unreadable, True, self.unreadable_color)
                self.screen.blit(text_surface, (text_x, y_pos))

    def _draw_base_stats(self):
        """Draw base stats in two columns"""
        if not self.character_data:
            return

        stats = self.character_data.get('effective_stats', {})
        base_stats = ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']

        # Position below vital stats
        stats_x = self.sheet_rect.x + self.equipment_width + 20
        stats_y = self.sheet_rect.y + 350
        col_width = 100

        for i, stat in enumerate(base_stats):
            col = i % 2
            row = i // 2
            x = stats_x + col * col_width
            y = stats_y + row * 25

            value = stats.get(stat, 0)
            text = f"{stat}: {value}"
            color = self._get_stat_color(value)

            text_surface = self.fonts['medium'].render(text, True, color)
            self.screen.blit(text_surface, (x, y))

    def _draw_additional_stats(self):
        """Draw additional stats like Age, Immunity, BMI"""
        if not self.character_data:
            return

        stats = self.character_data.get('effective_stats', {})

        # Position on the right side
        add_stats_x = self.sheet_rect.x + self.equipment_width + 250
        add_stats_y = self.sheet_rect.y + 200

        # Age (placeholder)
        age_text = f"Age: 18"
        age_surface = self.fonts['medium'].render(age_text, True, self.text_color)
        self.screen.blit(age_surface, (add_stats_x, add_stats_y))

        # Immunity
        immunity = stats.get('immune_system', 100)
        immunity_text = f"Immunity: {immunity}"
        immunity_surface = self.fonts['medium'].render(immunity_text, True, self.text_color)
        self.screen.blit(immunity_surface, (add_stats_x, add_stats_y + 30))

        # BMI (calculated from height and estimated weight)
        height_cm = stats.get('height', 170)
        estimated_weight = 70  # Placeholder
        bmi = estimated_weight / ((height_cm / 100) ** 2)
        bmi_text = f"BMI: {bmi:.1f}"
        bmi_surface = self.fonts['medium'].render(bmi_text, True, self.text_color)
        self.screen.blit(bmi_surface, (add_stats_x, add_stats_y + 60))

    def _draw_traits_and_skills(self):
        """Draw traits and skills with purple cube icons"""
        if not self.character_data:
            return

        # Traits section
        traits_x = self.sheet_rect.x + self.equipment_width + 20
        traits_y = self.sheet_rect.y + 450

        traits_title = self.fonts['medium'].render("Traits:", True, self.gold_color)
        self.screen.blit(traits_title, (traits_x, traits_y))

        # Draw trait icons (purple cubes for now)
        dna_traits = self.character_data.get('dna_traits', {})
        trait_count = 0
        for i, (category, trait) in enumerate(dna_traits.items()):
            if trait_count < 9:  # Limit display
                icon_x = traits_x + (trait_count % 3) * 45
                icon_y = traits_y + 30 + (trait_count // 3) * 45

                icon_rect = pygame.Rect(icon_x, icon_y, 40, 40)
                self.screen.blit(self.placeholder_images['trait'], icon_rect)
                trait_count += 1

        # Skills section (placeholder)
        skills_x = traits_x + 150
        skills_title = self.fonts['medium'].render("Skills:", True, self.gold_color)
        self.screen.blit(skills_title, (skills_x, traits_y))

        # Draw skill icons (purple cubes for now)
        for i in range(6):  # Placeholder skills
            icon_x = skills_x + (i % 3) * 45
            icon_y = traits_y + 30 + (i // 3) * 45

            icon_rect = pygame.Rect(icon_x, icon_y, 40, 40)
            self.screen.blit(self.placeholder_images['skill'], icon_rect)

    def _get_stat_color(self, value: int) -> Tuple[int, int, int]:
        """Get color based on stat value"""
        if value >= 70:
            return self.green_color
        elif value >= 40:
            return self.text_color
        else:
            return self.red_color

    def update_readable_stats(self, character_data: Dict[str, Any]):
        """Update the readable stat lines based on character data"""
        self.readable_lines = []

        # Character info
        self.readable_lines.append(f"Name: {character_data.get('name', 'NO NAME')}")
        race = character_data.get('race', {})
        self.readable_lines.append(f"Race: {race.get('name', 'Unknown')} ({race.get('rarity', 'common')})")
        gender = character_data.get('gender', {})
        self.readable_lines.append(f"Gender: {gender.get('name', 'Unknown')}")
        self.readable_lines.append("Title: Vagabond")
        self.readable_lines.append("Class: Adventurer")

        # Base stats
        stats = character_data.get('effective_stats', {})
        for stat in ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']:
            value = stats.get(stat, 0)
            self.readable_lines.append(f"{stat}: {value}")

        # Combat stats
        combat_stats = self._calculate_combat_stats(stats)
        self.readable_lines.append("=== COMBAT STATS ===")
        self.readable_lines.append(f"Melee Physical: {combat_stats['melee_physical']}")
        self.readable_lines.append(f"Critical Chance: {combat_stats['critical_chance']}%")
        self.readable_lines.append(f"Attack Speed: {combat_stats['attack_speed']}")

        # Resist stats
        self.readable_lines.append("=== RESIST STATS ===")
        self.readable_lines.append(f"Fire Resist: {combat_stats['fire_resist']}%")
        self.readable_lines.append(f"Physical Resist: {combat_stats['blunt_resist']}%")

        # Biology stats
        biology_stats = self._calculate_biology_stats(stats)
        self.readable_lines.append("=== BIOLOGY STATS ===")
        self.readable_lines.append(f"Beauty: {biology_stats['beauty']}")
        self.readable_lines.append(f"Height: {biology_stats['height']} cm")
        self.readable_lines.append(f"Weight: {biology_stats['weight']} kg")
        self.readable_lines.append(f"BMI: {biology_stats['bmi']:.1f}")

        # DNA Traits
        dna_traits = character_data.get('dna_traits', {})
        for category, trait in dna_traits.items():
            trait_line = f"{trait['category_name']}: {trait['name']} ({trait['rarity']})"
            self.readable_lines.append(trait_line)

        # Pad with empty lines if needed
        while len(self.readable_lines) < 50:
            self.readable_lines.append("")

    def _draw_character_info_section(self):
        """Draw character name, class, level section"""
        if not self.character_data:
            return

        # Header section
        header_rect = pygame.Rect(self.sheet_rect.x + 20, self.sheet_rect.y + 20,
                                 self.sheet_rect.width - 40, 80)
        pygame.draw.rect(self.screen, self.header_color, header_rect)
        pygame.draw.rect(self.screen, self.border_color, header_rect, 1)

        # Character name
        name = self.character_data.get('name', 'NO NAME')
        name_text = self.fonts['large'].render(name, True, self.gold_color)
        name_rect = name_text.get_rect(centerx=header_rect.centerx, y=header_rect.y + 10)
        self.screen.blit(name_text, name_rect)

        # Class and level
        class_text = self.fonts['medium'].render("Class: Adventurer", True, self.text_color)
        level_text = self.fonts['medium'].render("Lv. 1", True, self.text_color)
        status_text = self.fonts['medium'].render("Status: Vagabond", True, self.text_color)

        self.screen.blit(class_text, (header_rect.x + 20, header_rect.y + 45))
        self.screen.blit(level_text, (header_rect.centerx - 30, header_rect.y + 45))
        self.screen.blit(status_text, (header_rect.right - 200, header_rect.y + 45))

    def _draw_equipment_section(self):
        """Draw equipment slots in a cleaner layout"""
        # Equipment panel
        eq_rect = pygame.Rect(self.sheet_rect.x + 20, self.sheet_rect.y + 120, 200, 400)
        pygame.draw.rect(self.screen, self.header_color, eq_rect)
        pygame.draw.rect(self.screen, self.border_color, eq_rect, 1)

        # Equipment title
        eq_title = self.fonts['medium'].render("Equipment", True, self.gold_color)
        self.screen.blit(eq_title, (eq_rect.x + 10, eq_rect.y + 10))

        # Draw equipment slots in grid
        slot_size = 45
        slots_per_row = 3
        start_x = eq_rect.x + 15
        start_y = eq_rect.y + 45

        slot_names = ['helmet', 'shoulder', 'chest', 'legs', 'belt', 'boots',
                     'gloves', 'main_hand', 'off_hand', 'ring1', 'ring2', 'necklace']

        for i, slot_name in enumerate(slot_names):
            row = i // slots_per_row
            col = i % slots_per_row
            x = start_x + col * (slot_size + 5)
            y = start_y + row * (slot_size + 5)

            slot_rect = pygame.Rect(x, y, slot_size, slot_size)
            pygame.draw.rect(self.screen, (30, 30, 40), slot_rect)
            pygame.draw.rect(self.screen, self.border_color, slot_rect, 1)

            # Draw placeholder
            placeholder = pygame.transform.scale(self.placeholder_images['equipment'], (slot_size-4, slot_size-4))
            self.screen.blit(placeholder, (x + 2, y + 2))

    def _draw_stats_section(self, reveal_progress: float):
        """Draw main stats section with HP/MP bars"""
        if not self.character_data:
            return

        stats = self.character_data.get('effective_stats', {})

        # Stats panel
        stats_rect = pygame.Rect(self.sheet_rect.x + 240, self.sheet_rect.y + 120, 300, 200)
        pygame.draw.rect(self.screen, self.header_color, stats_rect)
        pygame.draw.rect(self.screen, self.border_color, stats_rect, 1)

        # Character portrait above HP bars
        portrait_rect = pygame.Rect(stats_rect.x + 10, stats_rect.y + 10, 60, 60)
        if self.character_data:
            race = self.character_data.get('race', {}).get('name', 'unknown').lower()
            gender = self.character_data.get('gender', {}).get('name', 'unknown').lower()
            portrait = self.load_character_image(race, gender)
            portrait_scaled = pygame.transform.scale(portrait, (60, 60))
            self.screen.blit(portrait_scaled, portrait_rect)
            pygame.draw.rect(self.screen, self.border_color, portrait_rect, 2)

        # HP/MP/XP bars below portrait
        bars_x = stats_rect.x + 80
        bars_y = stats_rect.y + 15

        max_hp = stats.get('VIT', 20) * 10 + 100
        max_mp = stats.get('WILL', 20) * 5 + 50

        self._draw_clean_stat_bar(bars_x, bars_y, 180, 20, max_hp, max_hp, self.red_color, "HP")
        self._draw_clean_stat_bar(bars_x, bars_y + 25, 180, 20, max_mp, max_mp, self.blue_color, "MP")
        self._draw_clean_stat_bar(bars_x, bars_y + 50, 180, 20, 0, 1000, self.gold_color, "XP")

        # Base stats
        base_stats = ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']
        stats_start_y = stats_rect.y + 100

        for i, stat in enumerate(base_stats):
            col = i % 2
            row = i // 2
            x = stats_rect.x + 20 + col * 120
            y = stats_start_y + row * 25

            value = stats.get(stat, 0)
            revealed_line = int(reveal_progress * len(self.readable_lines))

            if i + 2 < revealed_line:  # +2 for race and gender lines
                text = f"{stat}: {value}"
                color = self._get_stat_color(value)
                stat_surface = self.fonts['medium'].render(text, True, color)
                self.screen.blit(stat_surface, (x, y))
            else:
                # Show mystical symbols
                symbol_text = self.unreadable_lines[i + 2] if i + 2 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['medium'].render(symbol_text[:8], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (x, y))

    def _draw_clean_stat_bar(self, x: int, y: int, width: int, height: int,
                            current: int, maximum: int, color: tuple, label: str):
        """Draw a clean stat bar"""
        # Background
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(self.screen, (20, 20, 25), bg_rect)
        pygame.draw.rect(self.screen, self.border_color, bg_rect, 1)

        # Fill
        if maximum > 0:
            fill_width = int((current / maximum) * (width - 2))
            fill_rect = pygame.Rect(x + 1, y + 1, fill_width, height - 2)
            pygame.draw.rect(self.screen, color, fill_rect)

        # Text
        text = f"{label}: {current}/{maximum}"
        text_surface = self.fonts['small'].render(text, True, self.text_color)
        text_rect = text_surface.get_rect(center=bg_rect.center)
        self.screen.blit(text_surface, text_rect)

    def _draw_combat_stats_section(self, reveal_progress: float):
        """Draw combat stats section"""
        if not self.character_data:
            return

        stats = self.character_data.get('effective_stats', {})

        # Combat stats panel
        combat_rect = pygame.Rect(self.sheet_rect.x + 560, self.sheet_rect.y + 120, 250, 200)
        pygame.draw.rect(self.screen, self.header_color, combat_rect)
        pygame.draw.rect(self.screen, self.border_color, combat_rect, 1)

        # Title
        title_surface = self.fonts['medium'].render("Combat", True, self.gold_color)
        self.screen.blit(title_surface, (combat_rect.x + 10, combat_rect.y + 10))

        # Combat stats
        combat_stats = [
            ("P. Atk.", stats.get('STR', 0) * 2),
            ("P. Def.", stats.get('VIT', 0) + 50),
            ("P. Accuracy", 50 + stats.get('DEX', 0) // 2),
            ("P. Evasion", stats.get('SPE', 0) // 2),
            ("P. Critical", stats.get('DEX', 0) // 3),
            ("Atk. Spd.", 350 + stats.get('SPE', 0)),
            ("Speed", stats.get('SPE', 0) + 100)
        ]

        magic_stats = [
            ("M. Atk.", stats.get('magic_affinity', 0) + stats.get('INT', 0)),
            ("M. Def.", stats.get('WILL', 0) + 30),
            ("M. Accuracy", 50 + stats.get('INT', 0) // 2),
            ("M. Evasion", stats.get('WILL', 0) // 2),
            ("M. Critical", stats.get('INT', 0) // 3),
            ("Casting Spd.", 300 + stats.get('WILL', 0))
        ]

        # Draw combat stats
        y_offset = 40
        revealed_line = int(reveal_progress * len(self.readable_lines))

        for i, (stat_name, value) in enumerate(combat_stats + magic_stats):
            if i + 8 < revealed_line:  # Offset for previous sections
                text = f"{stat_name}: {value}"
                stat_surface = self.fonts['small'].render(text, True, self.text_color)
                self.screen.blit(stat_surface, (combat_rect.x + 10, combat_rect.y + y_offset))
            else:
                # Show symbols
                symbol_text = self.unreadable_lines[i + 8] if i + 8 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['small'].render(symbol_text[:10], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (combat_rect.x + 10, combat_rect.y + y_offset))

            y_offset += 18
            if i == 6:  # Add spacing between physical and magical stats
                y_offset += 10

    def _draw_traits_section(self, reveal_progress: float):
        """Draw traits and additional stats section"""
        if not self.character_data:
            return

        # Traits panel
        traits_rect = pygame.Rect(self.sheet_rect.x + 240, self.sheet_rect.y + 340, 570, 150)
        pygame.draw.rect(self.screen, self.header_color, traits_rect)
        pygame.draw.rect(self.screen, self.border_color, traits_rect, 1)

        # Title
        title_surface = self.fonts['medium'].render("Traits & Additional Stats", True, self.gold_color)
        self.screen.blit(title_surface, (traits_rect.x + 10, traits_rect.y + 10))

        # Draw trait icons
        dna_traits = self.character_data.get('dna_traits', {})
        icon_size = 35
        icons_per_row = 8
        start_x = traits_rect.x + 20
        start_y = traits_rect.y + 40

        revealed_line = int(reveal_progress * len(self.readable_lines))

        for i, (category, trait) in enumerate(dna_traits.items()):
            if i < 16:  # Limit display
                row = i // icons_per_row
                col = i % icons_per_row
                x = start_x + col * (icon_size + 5)
                y = start_y + row * (icon_size + 5)

                icon_rect = pygame.Rect(x, y, icon_size, icon_size)

                if i + 15 < revealed_line:  # Show trait icon
                    pygame.draw.rect(self.screen, (60, 40, 80), icon_rect)  # Purple background
                    pygame.draw.rect(self.screen, self.border_color, icon_rect, 1)

                    # Draw trait symbol/icon
                    trait_surface = self.fonts['small'].render(trait['category_icon'], True, self.text_color)
                    trait_rect = trait_surface.get_rect(center=icon_rect.center)
                    self.screen.blit(trait_surface, trait_rect)
                else:
                    # Show mystical symbol
                    pygame.draw.rect(self.screen, (40, 40, 50), icon_rect)
                    pygame.draw.rect(self.screen, self.unreadable_color, icon_rect, 1)

                    symbol_text = self.unreadable_lines[i + 15] if i + 15 < len(self.unreadable_lines) else "◊"
                    symbol_surface = self.fonts['small'].render(symbol_text[0], True, self.unreadable_color)
                    symbol_rect = symbol_surface.get_rect(center=icon_rect.center)
                    self.screen.blit(symbol_surface, symbol_rect)

        # Additional stats on the right
        stats = self.character_data.get('effective_stats', {})
        add_stats_x = traits_rect.x + 350
        add_stats_y = traits_rect.y + 40

        additional_stats = [
            f"Age: 18",
            f"Immunity: {stats.get('immune_system', 100)}",
            f"Beauty: {stats.get('beauty', 50)}",
            f"Height: {stats.get('height', 170)} cm",
            f"Magic Affinity: {stats.get('magic_affinity', 0)}"
        ]

        for i, stat_text in enumerate(additional_stats):
            if i + 25 < revealed_line:
                stat_surface = self.fonts['small'].render(stat_text, True, self.text_color)
                self.screen.blit(stat_surface, (add_stats_x, add_stats_y + i * 20))
            else:
                symbol_text = self.unreadable_lines[i + 25] if i + 25 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['small'].render(symbol_text[:12], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (add_stats_x, add_stats_y + i * 20))

    def _draw_header_section(self):
        """Draw clean header section"""
        if not self.character_data:
            return

        header_rect = pygame.Rect(40, 40, SCREEN_WIDTH - 80, 60)
        pygame.draw.rect(self.screen, self.section_color, header_rect)
        pygame.draw.rect(self.screen, self.border_color, header_rect, 1)

        # Character name
        name = self.character_data.get('name', 'NO NAME')
        name_surface = self.fonts['large'].render(name, True, self.gold_color)
        self.screen.blit(name_surface, (header_rect.x + 20, header_rect.y + 15))

        # Class and level on the right
        class_surface = self.fonts['medium'].render("Adventurer - Level 1", True, self.text_color)
        class_rect = class_surface.get_rect(right=header_rect.right - 20, centery=header_rect.centery)
        self.screen.blit(class_surface, class_rect)

    def _draw_left_panel(self):
        """Draw left panel with equipment"""
        # Equipment panel - much wider for larger slots
        panel_rect = pygame.Rect(40, 120, 280, 480)  # Increased width from 200 to 280
        pygame.draw.rect(self.screen, self.section_color, panel_rect)
        pygame.draw.rect(self.screen, self.border_color, panel_rect, 1)

        # Title
        title_surface = self.fonts['medium'].render("Equipment", True, self.gold_color)
        self.screen.blit(title_surface, (panel_rect.x + 10, panel_rect.y + 10))

        # Equipment slots arranged like human body - much larger slots
        slot_size = 50  # Increased from 30 to 50
        center_x = panel_rect.x + panel_rect.width // 2
        start_y = panel_rect.y + 50  # Start higher since no portrait

        # Define slot positions in human-like arrangement with better spacing
        slot_positions = {
            'helmet': (center_x - slot_size//2, start_y),  # Head
            'necklace': (center_x - slot_size//2, start_y + 65),  # Neck - more space
            'shoulder': (center_x - slot_size - 20, start_y + 100),  # Left shoulder - more space
            'chest': (center_x - slot_size//2, start_y + 130),  # Chest - more space
            'gloves': (center_x - slot_size - 60, start_y + 160),  # Left hand - more space
            'main_hand': (center_x + 60, start_y + 160),  # Right hand - more space
            'belt': (center_x - slot_size//2, start_y + 190),  # Belt - more space
            'legs': (center_x - slot_size//2, start_y + 250),  # Legs - more space
            'ring1': (center_x - slot_size - 90, start_y + 190),  # Left ring - more space
            'ring2': (center_x + 90, start_y + 190),  # Right ring - more space
            'boots': (center_x - slot_size//2, start_y + 310),  # Feet - more space
            'off_hand': (center_x - slot_size - 90, start_y + 160)  # Left hand weapon/shield - more space
        }

        for slot_name, (x, y) in slot_positions.items():
            slot_rect = pygame.Rect(x, y, slot_size, slot_size)
            pygame.draw.rect(self.screen, (30, 32, 38), slot_rect)
            pygame.draw.rect(self.screen, self.accent_color, slot_rect, 2)  # Thicker border for larger slots

            # Add slot labels for clarity
            label_surface = self.fonts['tiny'].render(slot_name[:4], True, self.secondary_text)
            label_rect = label_surface.get_rect(center=(x + slot_size//2, y + slot_size + 15))
            self.screen.blit(label_surface, label_rect)

    def _draw_center_panel(self, reveal_progress: float):
        """Draw center panel with main stats"""
        if not self.character_data:
            return

        panel_rect = pygame.Rect(340, 120, 300, 280)  # Moved right to accommodate wider equipment panel
        pygame.draw.rect(self.screen, self.section_color, panel_rect)
        pygame.draw.rect(self.screen, self.border_color, panel_rect, 1)

        # Title
        title_surface = self.fonts['medium'].render("Character Stats", True, self.gold_color)
        self.screen.blit(title_surface, (panel_rect.x + 10, panel_rect.y + 10))

        # Character portrait in the panel
        race = self.character_data.get('race', {}).get('name', 'unknown').lower()
        gender = self.character_data.get('gender', {}).get('name', 'unknown').lower()
        portrait = self.load_character_image(race, gender)

        # Portrait position - left side of panel
        portrait_rect = pygame.Rect(panel_rect.x + 15, panel_rect.y + 40, 80, 80)
        portrait_scaled = pygame.transform.scale(portrait, (80, 80))
        self.screen.blit(portrait_scaled, portrait_rect)
        pygame.draw.rect(self.screen, self.border_color, portrait_rect, 2)

        # Character info next to portrait
        info_x = portrait_rect.right + 10
        info_y = portrait_rect.y

        character_name = self.character_data.get('name', 'NO NAME')
        race_name = self.character_data.get('race', {}).get('name', 'Unknown')
        gender_name = self.character_data.get('gender', {}).get('name', 'Unknown')

        char_info = [
            ("Name", character_name),
            ("Race", race_name),
            ("Gender", gender_name),
            ("Title", "Vagabond"),
            ("Class", "Adventurer")
        ]

        # Draw character info with smaller spacing to fit in portrait height
        for i, (label, value) in enumerate(char_info):
            y = info_y + i * 15  # Smaller line height to fit in 80px portrait height
            if i + 8 < int(reveal_progress * len(self.readable_lines)):
                label_surface = self.fonts['tiny'].render(f"{label}:", True, self.label_color)
                value_surface = self.fonts['tiny'].render(str(value), True, self.text_color)
                self.screen.blit(label_surface, (info_x, y))
                self.screen.blit(value_surface, (info_x + 50, y))
            else:
                symbol_text = self.unreadable_lines[i + 8] if i + 8 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['tiny'].render(symbol_text[:8], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (info_x, y))

        # HP/MP/XP bars below portrait
        stats = self.character_data.get('effective_stats', {})
        bars_x = panel_rect.x + 15
        bars_y = portrait_rect.bottom + 10

        max_hp = stats.get('hp', stats.get('VIT', 20) * 10 + 100)
        max_mp = stats.get('WILL', 20) * 5 + 50

        self._draw_modern_stat_bar(bars_x, bars_y, 270, 18, max_hp, max_hp, self.red_color, "HP")
        self._draw_modern_stat_bar(bars_x, bars_y + 25, 270, 18, max_mp, max_mp, self.blue_color, "MP")
        self._draw_modern_stat_bar(bars_x, bars_y + 50, 270, 18, 0, 1000, self.gold_color, "XP")

        # Base stats below HP bars
        base_stats = ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']
        stats_start_y = bars_y + 80  # Position below HP/MP/XP bars

        for i, stat in enumerate(base_stats):
            col = i % 2  # 2 columns for better spacing
            row = i // 2
            x = panel_rect.x + 15 + col * 130  # Wider spacing
            y = stats_start_y + row * 30  # Compact vertical space

            value = stats.get(stat, 0)
            revealed_line = int(reveal_progress * len(self.readable_lines))

            if i + 2 < revealed_line:
                # Draw stat background with better styling
                stat_bg = pygame.Rect(x, y, 120, 25)
                pygame.draw.rect(self.screen, (30, 35, 42), stat_bg)
                pygame.draw.rect(self.screen, self.border_color, stat_bg, 1)

                # Color code the stat value
                if value >= 70:
                    value_color = self.green_color
                elif value >= 50:
                    value_color = self.text_color
                elif value >= 30:
                    value_color = self.orange_color
                else:
                    value_color = self.red_color

                # Draw stat label and value separately for better alignment
                label_surface = self.fonts['small'].render(f"{stat}:", True, self.label_color)
                value_surface = self.fonts['small'].render(str(value), True, value_color)

                self.screen.blit(label_surface, (x + 5, y + 5))
                self.screen.blit(value_surface, (x + 70, y + 5))
            else:
                # Show mystical symbols
                symbol_text = self.unreadable_lines[i + 2] if i + 2 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['small'].render(symbol_text[:8], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (x, y))

        # Character info is now displayed next to portrait above

    def _draw_modern_stat_bar(self, x: int, y: int, width: int, height: int,
                             current: int, maximum: int, color: tuple, label: str):
        """Draw modern styled stat bar"""
        # Background
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(self.screen, (20, 22, 28), bg_rect)
        pygame.draw.rect(self.screen, self.accent_color, bg_rect, 1)

        # Fill
        if maximum > 0:
            fill_width = int((current / maximum) * (width - 2))
            fill_rect = pygame.Rect(x + 1, y + 1, fill_width, height - 2)
            pygame.draw.rect(self.screen, color, fill_rect)

        # Text
        text = f"{label}: {current}/{maximum}"
        text_surface = self.fonts['small'].render(text, True, self.text_color)
        text_rect = text_surface.get_rect(center=bg_rect.center)
        self.screen.blit(text_surface, text_rect)

    def _draw_right_panel(self, reveal_progress: float):
        """Draw right panel with detailed combat stats"""
        if not self.character_data:
            return

        panel_rect = pygame.Rect(660, 120, 300, 480)  # Moved right to accommodate repositioned center panel
        pygame.draw.rect(self.screen, self.section_color, panel_rect)
        pygame.draw.rect(self.screen, self.border_color, panel_rect, 1)

        # Title
        title_surface = self.fonts['medium'].render("COMBAT", True, self.gold_color)
        self.screen.blit(title_surface, (panel_rect.x + 10, panel_rect.y + 10))

        stats = self.character_data.get('effective_stats', {})

        # Calculate combat stats
        combat_stats = self._calculate_combat_stats(stats)

        y_offset = 35
        revealed_line = int(reveal_progress * len(self.readable_lines))

        # Draw damage stats section
        damage_title = self.fonts['small'].render("Damage Stats:", True, self.gold_color)
        self.screen.blit(damage_title, (panel_rect.x + 10, panel_rect.y + y_offset))
        y_offset += 20

        # Show ALL damage stats including 0 values as requested
        damage_stats = [
            ("Melee Physical", combat_stats['melee_physical']),
            ("Ranged Physical", combat_stats['ranged_physical']),
            ("Magic Damage", combat_stats['magic_damage']),
            ("Fire", combat_stats['fire_damage']),
            ("Water", combat_stats['water_damage']),
            ("Earth", combat_stats['earth_damage']),
            ("Wind", combat_stats['wind_damage']),
            ("Nature", combat_stats.get('nature_damage', 0)),
            ("Arcane", combat_stats.get('arcane_damage', 0)),
            ("Holy", combat_stats['holy_damage']),
            ("Chaos", combat_stats['chaos_damage']),
            ("Critical Hit Chance", f"{combat_stats['critical_chance']}%"),
            ("Hit Chance", f"{combat_stats['hit_chance']}%"),
            ("Attack Speed", combat_stats['attack_speed']),
            ("Magic Cast Speed", combat_stats['cast_speed']),
            ("HP", combat_stats['hp']),
            ("Travel Speed", combat_stats['travel_speed'])
        ]

        for i, (stat_name, value) in enumerate(damage_stats):
            y = panel_rect.y + y_offset + i * 20  # Better spacing

            if i + 12 < revealed_line:
                # Color code values for better readability
                if isinstance(value, str) and '%' in value:
                    value_color = self.orange_color  # Percentages in orange
                elif isinstance(value, int) and value > 50:
                    value_color = self.green_color  # High values in green
                elif isinstance(value, int) and value > 0:
                    value_color = self.text_color  # Normal values
                else:
                    value_color = self.secondary_text  # Low/zero values

                # Draw with better formatting
                stat_surface = self.fonts['small'].render(f"{stat_name}:", True, self.label_color)
                value_surface = self.fonts['small'].render(str(value), True, value_color)

                self.screen.blit(stat_surface, (panel_rect.x + 10, y))
                self.screen.blit(value_surface, (panel_rect.x + 180, y))
            else:
                # Show symbols
                symbol_text = self.unreadable_lines[i + 12] if i + 12 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['small'].render(symbol_text[:12], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (panel_rect.x + 15, y))

        y_offset += len(damage_stats) * 18 + 15

        # Draw resist stats section
        resist_title = self.fonts['small'].render("% Resist Stats:", True, self.gold_color)
        self.screen.blit(resist_title, (panel_rect.x + 10, panel_rect.y + y_offset))
        y_offset += 20

        # Show ALL resist stats including 0% values as requested
        resist_stats = [
            ("Physical Blunt", f"{combat_stats['blunt_resist']}%"),
            ("Physical Slash", f"{combat_stats['slash_resist']}%"),
            ("Physical Pierce", f"{combat_stats['pierce_resist']}%"),
            ("Fire", f"{combat_stats['fire_resist']}%"),
            ("Water", f"{combat_stats['water_resist']}%"),
            ("Earth", f"{combat_stats['earth_resist']}%"),
            ("Wind", f"{combat_stats['wind_resist']}%"),
            ("Nature", f"{combat_stats.get('nature_resist', 0)}%"),
            ("Arcane", f"{combat_stats.get('arcane_resist', 0)}%"),
            ("Holy", f"{combat_stats['holy_resist']}%"),
            ("Chaos", f"{combat_stats['chaos_resist']}%")
        ]

        for i, (stat_name, value) in enumerate(resist_stats):
            y = panel_rect.y + y_offset + i * 20  # Better spacing

            # Show resist stats easier (reduce reveal requirement)
            if i + 15 < revealed_line or revealed_line > 20:
                # Color code resist values
                resist_val = int(value.replace('%', ''))
                if resist_val >= 50:
                    value_color = self.green_color
                elif resist_val >= 20:
                    value_color = self.orange_color
                elif resist_val > 0:
                    value_color = self.text_color
                else:
                    value_color = self.secondary_text

                stat_surface = self.fonts['small'].render(f"{stat_name}:", True, self.label_color)
                value_surface = self.fonts['small'].render(value, True, value_color)

                self.screen.blit(stat_surface, (panel_rect.x + 10, y))
                self.screen.blit(value_surface, (panel_rect.x + 180, y))
            else:
                symbol_text = self.unreadable_lines[i + 15] if i + 15 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['small'].render(symbol_text[:12], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (panel_rect.x + 15, y))

    def _calculate_combat_stats(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate all combat stats from base stats"""
        str_stat = stats.get('STR', 50)
        dex_stat = stats.get('DEX', 50)
        vit_stat = stats.get('VIT', 50)
        int_stat = stats.get('INT', 50)
        spe_stat = stats.get('SPE', 50)
        will_stat = stats.get('WILL', 50)
        magic_affinity = stats.get('magic_affinity', 50)

        # Use pre-calculated values if available, otherwise calculate
        combat_stats = {
            # Damage stats
            'melee_physical': stats.get('melee_damage', max(1, str_stat // 5)),
            'ranged_physical': stats.get('ranged_damage', 0),
            'magic_damage': stats.get('magic_damage', int_stat + int(magic_affinity * 0.5)),
            'fire_damage': stats.get('fire_damage_bonus', 0),
            'water_damage': stats.get('water_damage_bonus', 0),
            'earth_damage': stats.get('earth_damage_bonus', 0),
            'wind_damage': stats.get('air_damage_bonus', 0),
            'nature_damage': stats.get('nature_damage_bonus', 0),
            'arcane_damage': stats.get('arcane_damage_bonus', 0),
            'holy_damage': stats.get('light_damage_bonus', 0),
            'chaos_damage': stats.get('dark_damage_bonus', 0),

            # Combat mechanics
            'critical_chance': stats.get('critical_hit_chance', max(1, 5 + dex_stat // 10)),
            'critical_damage': 150 + dex_stat // 5,
            'hit_chance': stats.get('hit_chance', 75 + dex_stat // 4),
            'attack_speed': 100 + spe_stat // 2,
            'cast_speed': stats.get('magic_cast_speed', 100 + will_stat // 2),
            'hp': stats.get('hp', int((str_stat * 0.5) + (vit_stat * 1.0) + 50)),
            'travel_speed': stats.get('travel_speed', spe_stat),

            # Resist stats (max 90%)
            'blunt_resist': min(90, vit_stat // 10),
            'slash_resist': min(90, vit_stat // 10),
            'pierce_resist': min(90, vit_stat // 10),
            'fire_resist': min(90, stats.get('fire_resist_bonus', 0)),
            'water_resist': min(90, stats.get('water_resist_bonus', 0)),
            'earth_resist': min(90, stats.get('earth_resist_bonus', 0)),
            'wind_resist': min(90, stats.get('air_resist_bonus', 0)),
            'nature_resist': min(90, stats.get('nature_resist_bonus', 0)),
            'arcane_resist': min(90, stats.get('arcane_resist_bonus', 0)),
            'holy_resist': min(90, stats.get('light_resist_bonus', 0)),
            'chaos_resist': min(90, stats.get('dark_resist_bonus', 0))
        }

        return combat_stats

    def _draw_biology_panel(self, reveal_progress: float):
        """Draw biology stats panel"""
        if not self.character_data:
            return

        panel_rect = pygame.Rect(980, 120, 340, 480)  # Moved right to accommodate repositioned panels
        pygame.draw.rect(self.screen, self.section_color, panel_rect)
        pygame.draw.rect(self.screen, self.border_color, panel_rect, 1)

        # Title
        title_surface = self.fonts['medium'].render("Biology Stats", True, self.gold_color)
        self.screen.blit(title_surface, (panel_rect.x + 10, panel_rect.y + 10))

        stats = self.character_data.get('effective_stats', {})

        # Calculate biology stats
        biology_stats = self._calculate_biology_stats(stats)

        y_offset = 35
        revealed_line = int(reveal_progress * len(self.readable_lines))

        # Biology stats list - better organized and formatted
        bio_stats = [
            ("Age", f"{biology_stats['age']} (1 day)"),
            ("Beauty", str(biology_stats['beauty'])),
            ("Perception", str(biology_stats['perception'])),
            ("Persuasion", str(biology_stats['persuasion'])),
            ("Estimated Lifespan", f"{biology_stats['estimated_lifespan']} years"),
            ("IQ", str(biology_stats['iq'])),
            ("Height", f"{biology_stats['height']} cm"),
            ("Magic Affinity", str(biology_stats['magic_affinity'])),
            ("Load Capacity", f"{biology_stats['load_capacity']} kg"),
            ("Immune System", str(biology_stats['immune_system'])),
            ("Weight", f"{biology_stats['weight']} kg"),
            ("Daily Calorie Req.", f"{biology_stats['daily_calories']} cal"),
            ("BMI", f"{biology_stats['bmi']:.1f}")
        ]

        for i, (stat_name, value) in enumerate(bio_stats):
            y = panel_rect.y + y_offset + i * 24  # Better spacing

            # Always show biology stats (reduce reveal requirement)
            if i + 15 < revealed_line or revealed_line > 20:
                # Color code biology values
                if stat_name in ["Beauty", "Perception", "Persuasion", "IQ"]:
                    if isinstance(value, str) and value.isdigit():
                        val = int(value)
                        if val >= 70:
                            value_color = self.green_color
                        elif val >= 40:
                            value_color = self.text_color
                        else:
                            value_color = self.red_color
                    else:
                        value_color = self.text_color
                elif stat_name == "Estimated Lifespan":
                    value_color = self.orange_color  # Important stat
                else:
                    value_color = self.text_color

                # Draw with better formatting
                stat_surface = self.fonts['small'].render(f"{stat_name}:", True, self.label_color)
                value_surface = self.fonts['small'].render(str(value), True, value_color)

                self.screen.blit(stat_surface, (panel_rect.x + 10, y))
                self.screen.blit(value_surface, (panel_rect.x + 180, y))
            else:
                # Show symbols
                symbol_text = self.unreadable_lines[i + 15] if i + 15 < len(self.unreadable_lines) else "◊◈◇"
                symbol_surface = self.fonts['small'].render(symbol_text[:15], True, self.unreadable_color)
                self.screen.blit(symbol_surface, (panel_rect.x + 10, y))

    def _calculate_biology_stats(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate biology stats from base stats"""
        str_stat = stats.get('STR', 50)
        dex_stat = stats.get('DEX', 50)
        vit_stat = stats.get('VIT', 50)
        int_stat = stats.get('INT', 50)
        height = stats.get('height', 170)

        # Use pre-calculated values if available, otherwise calculate
        weight = stats.get('weight', max(40, ((height - 100) * 0.9) + (vit_stat - 50) * 0.2))
        bmi = stats.get('bmi', weight / ((height / 100) ** 2))
        daily_calories = stats.get('daily_calorie_requirement', int(weight * 25 + str_stat * 5))
        load_capacity = stats.get('load_capacity', int(str_stat * 0.8 + vit_stat * 0.3))

        biology_stats = {
            'age': 18,
            'age_days': '1 day',
            'beauty': stats.get('beauty', 50),
            'perception': stats.get('perception', 50),
            'persuasion': stats.get('persuasion', 50),
            'estimated_lifespan': stats.get('estimated_lifespan', 80),
            'iq': stats.get('iq', int_stat),
            'height': height,
            'magic_affinity': stats.get('magic_affinity', 50),
            'load_capacity': load_capacity,
            'immune_system': stats.get('immune_system', 100),
            'weight': int(weight),
            'daily_calories': daily_calories,
            'bmi': round(bmi, 1)
        }

        return biology_stats

    def _draw_bottom_panel(self, reveal_progress: float):
        """Draw bottom panel with traits"""
        if not self.character_data:
            return

        panel_rect = pygame.Rect(40, 620, 1200, 100)  # Wider, shorter panel
        pygame.draw.rect(self.screen, self.section_color, panel_rect)
        pygame.draw.rect(self.screen, self.border_color, panel_rect, 1)

        # Title
        title_surface = self.fonts['medium'].render("DNA Traits", True, self.gold_color)
        self.screen.blit(title_surface, (panel_rect.x + 10, panel_rect.y + 10))

        # Draw trait icons in a clean grid
        dna_traits = self.character_data.get('dna_traits', {})
        icon_size = 25
        icons_per_row = 20  # More icons per row
        start_x = panel_rect.x + 20
        start_y = panel_rect.y + 35

        revealed_line = int(reveal_progress * len(self.readable_lines))

        for i, (category, trait) in enumerate(dna_traits.items()):
            if i < 40:  # More traits can be displayed
                row = i // icons_per_row
                col = i % icons_per_row
                x = start_x + col * (icon_size + 5)
                y = start_y + row * (icon_size + 5)

                icon_rect = pygame.Rect(x, y, icon_size, icon_size)

                if i + 50 < revealed_line:
                    # Show trait icon with quality color
                    if trait['quality'] == 'positive':
                        bg_color = (40, 60, 40)
                    elif trait['quality'] == 'negative':
                        bg_color = (60, 40, 40)
                    else:
                        bg_color = (50, 50, 60)

                    pygame.draw.rect(self.screen, bg_color, icon_rect)
                    pygame.draw.rect(self.screen, self.border_color, icon_rect, 1)

                    # Draw trait icon
                    icon_surface = self.fonts['tiny'].render(trait['category_icon'], True, self.text_color)
                    icon_rect_center = icon_surface.get_rect(center=icon_rect.center)
                    self.screen.blit(icon_surface, icon_rect_center)
                else:
                    # Show mystical symbol
                    pygame.draw.rect(self.screen, (30, 32, 38), icon_rect)
                    pygame.draw.rect(self.screen, self.unreadable_color, icon_rect, 1)

                    symbol_text = self.unreadable_lines[i + 50] if i + 50 < len(self.unreadable_lines) else "◊"
                    symbol_surface = self.fonts['tiny'].render(symbol_text[0], True, self.unreadable_color)
                    symbol_rect = symbol_surface.get_rect(center=icon_rect.center)
                    self.screen.blit(symbol_surface, symbol_rect)
