"""
Audio Manager for One Life Isekai
Handles sound effects and background music
"""

import pygame
import os
from typing import Dict, Optional

class AudioManager:
    def __init__(self):
        """Initialize the audio manager"""
        pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        
        # Sound effect volumes
        self.sfx_volume = 0.7
        self.music_volume = 0.5
        
        # Sound cache
        self.sounds: Dict[str, pygame.mixer.Sound] = {}
        
        # Create default sounds if audio files don't exist
        self._create_default_sounds()
    
    def _create_default_sounds(self):
        """Create simple procedural sounds if audio files don't exist"""
        try:
            # Create simple click sound
            click_sound = self._generate_click_sound()
            self.sounds['click'] = click_sound
            
            # Create text reveal sound
            text_sound = self._generate_text_sound()
            self.sounds['text_reveal'] = text_sound
            
            # Create magical sound
            magic_sound = self._generate_magic_sound()
            self.sounds['magic'] = magic_sound
            
            # Create completion sound
            complete_sound = self._generate_complete_sound()
            self.sounds['complete'] = complete_sound
            
        except Exception as e:
            print(f"Warning: Could not create audio sounds: {e}")
    
    def _generate_click_sound(self) -> pygame.mixer.Sound:
        """Generate a simple click sound without numpy"""
        duration = 0.1
        sample_rate = 22050
        frames = int(duration * sample_rate)

        # Create a simple click sound using math
        import math
        frequency = 800
        sound_array = []

        for i in range(frames):
            t = i / sample_rate
            amplitude = math.sin(frequency * 2 * math.pi * t) * math.exp(-t * 20)
            sample = int(amplitude * 16383)  # 16-bit audio
            sound_array.append([sample, sample])  # Stereo

        # Convert to pygame sound
        sound = pygame.sndarray.make_sound(sound_array)
        return sound
    
    def _generate_text_sound(self) -> pygame.mixer.Sound:
        """Generate a subtle text reveal sound without numpy"""
        duration = 0.05
        sample_rate = 22050
        frames = int(duration * sample_rate)

        import math
        frequency = 1200
        sound_array = []

        for i in range(frames):
            t = i / sample_rate
            amplitude = math.sin(frequency * 2 * math.pi * t) * math.exp(-t * 30) * 0.3
            sample = int(amplitude * 16383)
            sound_array.append([sample, sample])

        sound = pygame.sndarray.make_sound(sound_array)
        return sound
    
    def _generate_magic_sound(self) -> pygame.mixer.Sound:
        """Generate a magical shimmer sound without numpy"""
        duration = 0.3
        sample_rate = 22050
        frames = int(duration * sample_rate)

        import math
        sound_array = []

        for i in range(frames):
            t = i / sample_rate
            # Multiple frequencies for magical effect
            wave1 = math.sin(800 * 2 * math.pi * t) * math.exp(-t * 5)
            wave2 = math.sin(1200 * 2 * math.pi * t) * math.exp(-t * 3) * 0.5
            wave3 = math.sin(1600 * 2 * math.pi * t) * math.exp(-t * 7) * 0.3

            amplitude = (wave1 + wave2 + wave3) * 0.4
            sample = int(amplitude * 16383)
            sound_array.append([sample, sample])

        sound = pygame.sndarray.make_sound(sound_array)
        return sound
    
    def _generate_complete_sound(self) -> pygame.mixer.Sound:
        """Generate a completion/success sound without numpy"""
        duration = 0.5
        sample_rate = 22050
        frames = int(duration * sample_rate)

        import math
        sound_array = []

        # Rising chord progression
        freq1 = 440  # A
        freq2 = 554  # C#
        freq3 = 659  # E

        for i in range(frames):
            t = i / sample_rate
            wave1 = math.sin(freq1 * 2 * math.pi * t) * math.exp(-t * 2)
            wave2 = math.sin(freq2 * 2 * math.pi * t) * math.exp(-t * 2) * 0.7
            wave3 = math.sin(freq3 * 2 * math.pi * t) * math.exp(-t * 2) * 0.5

            amplitude = (wave1 + wave2 + wave3) * 0.3
            sample = int(amplitude * 16383)
            sound_array.append([sample, sample])

        sound = pygame.sndarray.make_sound(sound_array)
        return sound
    
    def play_sound(self, sound_name: str, volume: Optional[float] = None):
        """Play a sound effect"""
        try:
            if sound_name in self.sounds:
                sound = self.sounds[sound_name]
                if volume is not None:
                    sound.set_volume(volume * self.sfx_volume)
                else:
                    sound.set_volume(self.sfx_volume)
                sound.play()
        except Exception as e:
            # Silently fail if sound can't be played
            pass
    
    def set_sfx_volume(self, volume: float):
        """Set sound effects volume (0.0 to 1.0)"""
        self.sfx_volume = max(0.0, min(1.0, volume))
    
    def set_music_volume(self, volume: float):
        """Set music volume (0.0 to 1.0)"""
        self.music_volume = max(0.0, min(1.0, volume))
        pygame.mixer.music.set_volume(self.music_volume)
    
    def load_sound(self, sound_name: str, file_path: str) -> bool:
        """Load a sound from file"""
        try:
            if os.path.exists(file_path):
                sound = pygame.mixer.Sound(file_path)
                self.sounds[sound_name] = sound
                return True
        except Exception as e:
            print(f"Could not load sound {sound_name} from {file_path}: {e}")
        return False
    
    def stop_all_sounds(self):
        """Stop all currently playing sounds"""
        pygame.mixer.stop()
    
    def cleanup(self):
        """Clean up audio resources"""
        pygame.mixer.quit()

# Global audio manager instance
audio_manager = AudioManager()
